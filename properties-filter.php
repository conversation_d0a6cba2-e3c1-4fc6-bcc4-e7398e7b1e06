<?php
/*
Plugin Name: Swift CRE Filter
Description: This plugin adds a shortcode to display properties filters.
Version: 1.0.0
Author: <PERSON><PERSON><PERSON><PERSON>
*/

const PLUGIN_FILE = __FILE__;
const WP_SWIFT_FILTER_VERSION = '1.0.0';

if (!defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/cpt-and-taxonomy.php';
require_once __DIR__ . '/shortcode.php';

// Enqueue CSS and JavaScript files
function swift_cre_filter_plugin_enqueue_scripts()
{
    // Enqueue CSS file
   wp_enqueue_style(
  'swift-filter-plugin-style',
  plugins_url('assets/css/swift-filter-style.css', __FILE__),
  array(),
  filemtime(plugin_dir_path(__FILE__) . 'assets/css/swift-filter-style.css')
);

    wp_enqueue_script("google-maps-api", "https://maps.googleapis.com/maps/api/js?key=AIzaSyBOTIG_lEDa4AJ07XR3A2QwpOs5N-IFEBg&sensor=false", [], null, true);

    wp_enqueue_script('swift-filter-map_infobox-script', plugins_url('assets/js/map_infobox.js', __FILE__), array('jquery'), '1.0', true);
    wp_enqueue_script('swift-filter-markerclusterer-script', plugins_url('assets/js/markerclusterer.js', __FILE__), array('jquery'), '1.0', true);

    wp_enqueue_script('swift-filter-plugin-script', plugins_url('assets/js/swift-filter-script.js', __FILE__), array('jquery'), '1.0', true);

    wp_localize_script("swift-filter-plugin-script", "SWIFT_FILTER_VARS", [
        "ajax_url" => admin_url("admin-ajax.php"),
        "plugin_assets" => plugins_url('assets/', __FILE__),
        "nonce" => wp_create_nonce("my-projects-nonce"),
    ]);
}

add_action('wp_enqueue_scripts', 'swift_cre_filter_plugin_enqueue_scripts');




// Properties Search Filter AJAX callback
add_action("wp_ajax_SwiftFilter", "property_filter_callback");
add_action("wp_ajax_nopriv_SwiftFilter", "property_filter_callback");

function property_filter_callback()
{
    $args = array(
        "post_type" => "our_properties",
        "order" => "ASC",
        "posts_per_page" => 12,
        "paged" => isset($_POST["paged"]) ? absint($_POST["paged"]) : 1,
    );

    // Initialize tax_query as an empty array
    $tax_query = array("relation" => "AND");

    // Handle transaction filter
    if (isset($_POST["transaction_type"]) && $_POST["transaction_type"]) {
        $transaction_types = explode(",", $_POST['transaction_type']);
        $tax_query[] = array(
            "taxonomy" => "transaction",
            "field" => "slug",
            "terms" => $transaction_types,
        );
    }

    // Handle type filter
    if (isset($_POST["property_type"]) && $_POST["property_type"]) {
        $property_types = explode(",", $_POST['property_type']);
        $tax_query[] = array(
            "taxonomy" => "property_type",
            "field" => "slug",
            "terms" => $property_types,
        );
    }

    // Initialize meta_query as an empty array
    $meta_query = array("relation" => "AND");

    // Handle price filter
    if (isset($_POST["min_price"]) && $_POST["min_price"] &&
        isset($_POST["max_price"]) && $_POST["max_price"]) {
        $meta_query[] = array(
            "key" => "salelease_price",
            "value" => array($_POST["min_price"], $_POST["max_price"]),
            "type" => "numeric",
            "compare" => "between",
        );
    } else {
        // Handle min price
        if (isset($_POST["min_price"]) && $_POST["min_price"]) {
            $meta_query[] = array(
                "key" => "salelease_price",
                "value" => $_POST["min_price"],
                "type" => "numeric",
                "compare" => ">=",
            );
        }

        // Handle max price
        if (isset($_POST["max_price"]) && $_POST["max_price"]) {
            $meta_query[] = array(
                "key" => "salelease_price",
                "value" => $_POST["max_price"],
                "type" => "numeric",
                "compare" => "<=",
            );
        }
    }

    // Set tax_query and meta_query in $args
    $args["tax_query"] = $tax_query;
    $args["meta_query"] = $meta_query;

    // Execute the query
    $loop = new WP_Query($args);
    
    // Initialize $html
    $html = "";

    if ($loop->have_posts()) {
        while ($loop->have_posts()) {
            $loop->the_post();
            // Retrieve property data
            $image = wp_get_attachment_image_src(get_post_thumbnail_id(), "single-post-thumbnail");
            $permalink = get_permalink();
			$address = get_field("address");
            $city_state = get_field("city_state");
            $price = get_field("salelease_price");
            $b_size = get_field("b_size");
            $l_size = get_field("l_size");
            $units = get_field("units");
            $types = get_the_terms(get_the_ID(), 'property_type');
            $transactions = get_the_terms(get_the_ID(), 'transaction');
            
            // Get type name
            $type_name = ($types && !is_wp_error($types) && !empty($types)) ? $types[0]->name : '';

            // Get transaction name
            $transaction_name = ($transactions && !is_wp_error($transactions) && !empty($transactions)) ? $transactions[0]->name : '';
            
            // Build HTML for each property
$html .= '<a class="project-item" href="' . $permalink . '">';
$html .= 
$html .= '<span class="project-pill">' . $transaction_name . '</span>';
$html .= '<div class="project-abs">';
$html .= '<table class="table"><tbody>';
$html .= $b_size ? '<tr><td>Building Size</td><td>' . $b_size . '</td></tr>' : '';
$html .= $l_size ? '<tr><td>Lot Size</td><td>' . $l_size . '</td></tr>' : '';
$html .= $units ? '<tr><td>Units</td><td>' . $units . '</td></tr>' : '';
$html .= $type_name ? '<tr><td>Property Type</td><td>' . $type_name . '</td></tr>' : '';
$html .= $transaction_name ? '<tr><td>Transaction Type</td><td>' . $transaction_name . '</td></tr>' : '';
$html .= '</tbody></table>';
$html .= $price ? '<h3 class="boldTag">$' . number_format($price) . '</h3>' : '';
$html .= '</div>';
$html .= '<div class="project-img position-relative">';
$html .= '<img src="' . ($image ? $image[0] : '') . '" alt="images" class="img-fluid" style="aspect-ratio: 7 / 4; object-fit:cover;">';
$html .= '</div>';
$html .= '<div class="project-footer">';
$html .= '<h2>' . $address . ', ' . ($city_state ? $city_state : '')  . '</h2>';
$html .= '<h4 class="mt-2">' .  get_the_title() . '</h4>';
$html .='<div class="mt-4 d-flex flex-wrap align-items-center gap-2 text-prime ">' .
  ($price ? '<span>$' . number_format($price) . '</span>' : '') .
  ($b_size ? '<span>—</span><span>' . $b_size . '</span>' : '') .
  ($type_name ? '<span>—</span><span>' . $type_name . '</span>' : '') .
'</div>';
$html .= '</div></a>';
        }
    } else {
        $html = "<p>No properties found.</p>";
    }

    // Prepare data to be sent as JSON
    $data = array(
        "html" => $html,
    );

    // Send JSON response and exit
    wp_send_json($data);
    wp_die();
}

