.filter-select {
    position: relative;
    border: 1px solid #999999;
    border-radius: 3px;
    float: left;
    width: 100%;
    background: #ffffff;
    padding: 5px 10px;
    margin-top: 5px;
}

.filter-select-arrow {
    position: absolute;
    top: 17px;
    right: 5px;
    border-top: 6px solid #666666;
    border-left: 5px solid transparent;
    border-bottom: 6px solid transparent;
    border-right: 5px solid transparent;
    z-index: 1;
}

.filter-onclick {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: transparent;
    cursor: pointer;
    z-index: 1;
    height: 40px;
}

.filter-dropdown {
    background: #ffffff;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.24);
    position: absolute;
    width: 100%;
    padding: 10px 10px;
    top: 38px;
    left: 0;
    z-index: 2;
}
.filter-dropdown label {
    display: inline-block;
}
.filter-dropdown .bts-dropdown {
    padding: 0!important;
}

.filter-dropdown .bts-dropdown li {
    list-style: none;
}

.filter-placeholder {
    font-size: 12px;
    display: block;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.filter-price-range .filter-extra-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
ol, ul {
	list-style: none;
}
.content {
	width:100%;
	position:relative;
	z-index:4;
	float:left;
	background:#fff;
}
.fl-wrap {
	float:left;
	position:relative;
}
.map-popup-wrap {
	display:block;
	width:300px !important;
	position:relative;
	margin-bottom: -10px;
	margin-left: -3px;
}
.map-popup {
	display:block;
	width:100%;
	position:relative;
	overflow:hidden;
	border-radius:10px;
	box-shadow: 0 9px 16px rgba(58, 87, 135, 0.15);
}
.map-popup-wrap:before {
	top: 100%;
	left: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	margin-top:-1px;
	z-index:20;
}
.map-popup-wrap:before {
	border-color:  transparent;
	border-top-color: #fff;
	border-width: 15px;
	margin-left: -15px;
}
.map-popup img {
	width:100%;
	height: 230px;
	-webkit-transition: all 2000ms cubic-bezier(.19,1,.22,1) 0ms;
	-webkit-transform: translateZ(0);
	transform: translateZ(0);
	transition: all 2000ms cubic-bezier(.19,1,.22,1) 0ms;
}
.listing-img-content:hover   img {
	-webkit-transform: scale(1.15);
	-moz-transform:scale(1.15);
	transform: scale(1.15);
}
.listing-content {
	background:#fff;
	padding:25px 20px;
	z-index:20;
	border-bottom-left-radius:10px;
	border-bottom-right-radius:10px;
	width: 100%;
}
.infoBox-close {
	position:absolute;
	width:30px;
	height:30px;
	line-height:30px;
	top:20px;
	right:20px;
	color:#fff;
	border-radius:100%;
	z-index:20;
	box-shadow:0px 0px 0px 5px rgba(255,255,255,0.4);
	cursor:pointer;
	padding: 0 10px;
}
.infoBox-close:hover {
	background:#fff;
	color:#000;
}
.listing-title h4{
	float: left;
	width: 100%;
	text-align: left;
	font-size: 16px;
	font-weight: 600;
	color: #592f38;
	padding-bottom: 5px;
	margin: 0;
}
.listing-title h4 a {
	color: #ec2626;
}
.listing-img-content {
	overflow:hidden;
}
.listing-img-content:before {
	content:'';
	position:absolute;
	top:0;
	left:0;
	z-index:1;
	width:100%;
	height:100%;
	background:#292929;
	opacity:0.3;
}
.cluster img{
	display:none
}
.cluster{
	width:40px!important;
	height:40px!important;
}
.cluster div{
	text-align:center;
	font-size:15px!important;
	color:#fff!important;
	font-weight:600;
	border-radius:100%;
	width:40px!important;
	height:40px!important;
	line-height:38px!important;
	box-shadow: 0px 0px 0px 4px rgba(0,0,0,0.1);
	border:2px solid  #fff;
	-webkit-transition: all 300ms linear;
	transition: all 100ms 3inear;
	animation:cluster-animation 1.5s infinite;
}
.cluster div:hover {
	background:#5d6275;
}
@keyframes cluster-animation{0%,100%{box-shadow: 0px 0px 0px 4px rgba(0,0,0,0.1);}50%{box-shadow: 0px 0px 0px 9px rgba(0,0,0,0.1);}}
.map-popup-location-phone , .map-popup-location-info {
	float:left;
	color:#666;
	font-size:13px;
	width:100%;
	text-align:left;
	margin-bottom:5px;
}
.map-popup-location-phone i , .map-popup-location-info i{
	padding-right:10px;
	font-size:14px;
	color: #ec2626;
}
.map-card-rainting {
	position:absolute;
	z-index:10;
	left:20px;
	top:-28px;
}
.map-card-rainting i {
	float:left;
	color:#FACC39;
	margin-right:6px;
}
.map-popup-reviews-count {
	color: rgba(255,255,255,0.9);
	position:relative;
	top:-4px;
}
.map-popup-category {
	position:absolute;
	top:20px;
	left:20px;
	font-weight:500;
	color:#fff;
	z-index:20;
	padding:10px 12px;
	border-radius:4px;
	box-shadow: 0px 0px 0px 3px rgba(255,255,255,0.2);
}
/*-------------Map---------------------------------------*/
.map-container {
	float:left;
	width:100%;
	position:relative;
	overflow: hidden;
}
.map-container.column-map {
	width:100%;
	-webkit-transform: translate3d(0,0,0);
	overflow: hidden;
	height: 74vh;
}
.map-container.column-map.right-pos-map {
	right:0;
}
.map-container.column-map.left-pos-map {
	left:0;
}
.map-container #map-main{
	position: absolute;
	top:0;
	left:0;
	height: 100%;
	width:100%;
	z-index: 10;
	overflow:hidden;
}
.mapzoom-in  , .mapzoom-out{
	position:fixed;
	background: #ED2224;
	right: 10px;
	z-index:100;
	top:50%;
	cursor:pointer;
	width:40px;
	height:40px;
	border-radius:100%;
	color:#fff;
	line-height:40px;
	margin-top:-20px;
	box-shadow:0px 0px 0px 5px rgba(255,255,255,0.4);
	-webkit-transform: translate3d(0,0,0);
	padding: 0 16px;
}
.mapzoom-in:before  , .mapzoom-out:before{
	font-family: FontAwesome;
	font-style: normal;
	font-weight: normal;
	text-decoration: inherit;
	content: "\f068";
}
.mapzoom-in:before{
	content: "\f067";
}
.mapzoom-in {
	margin-top:-80px;
}
.mapnavigation li {
	border: none;
	outline: 0;
	font-weight: inherit;
	font-style: inherit;
	font-size: 100%;
	font-family: inherit;
	vertical-align: baseline;
	text-decoration: none;
	margin: 0;
	padding: 0;
}
.mapnavigation {
	position:absolute;
	bottom:50px;
	right:7px;
	z-index:30;
	width:170px;
}
.mapnavigation a {
	width:70px;
	padding:5px 20px;
	border-radius:4px;
	color:#fff;
	float:left;
	margin-left:10px;
	text-decoration: none;
	box-shadow:0px 0px 0px 4px rgba(255,255,255,0.4);
}
.mapnavigation a , .mapzoom-in:hover  , .mapzoom-out:hover{
	background: #ec2626;
}
.map-popup-category,
.infoBox-close,
.cluster div
{
	background: #ED2224;
}

.mt-7{
	margin-top:7em;
}

.projectsTop{
    background: #eeeeee;
    margin-bottom: 1.5em;
}
.projectsTop .form-fields{
    background: #ffffff;
}
.projectsTop .form-fields:focus{border-color:var(--red);}
.projectsTop input.form-fields{
    padding-block:7px;
}


.myProperties #map{
	height:630px;
}

.myProperties .gm-style img {
    max-width: none;
    width: 100%;
}
.myProperties .propertyBox{
	position:relative;
	
}
.myProperties .propertyBox:before{
	position:absolute;
	content:'';
	width:40px;
	height:2px;
	background:var(--red);
	left:0;
	bottom:0;
	transition:all .3s;
	
}
.myProperties .propertyBox:hover:before{width:100%;}
.myProperties span.icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    color: #ffffff;
    font-size: 24px;
    width: 100%;
    height: 100%;
    display: flex;
    background: rgba(0,0,0,.4);
    justify-content: center;
    align-items: center;
	opacity:0;
	transition:all .3s;
}

.myProperties .propertyBox:hover span.icon {
	opacity:1;
	
}
.myProperties span.badge {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    background: #000000;
    border-radius: 0;
    font-weight: 400;
    padding: 8px 10px;
}



.myProperties .propertData h2 {
    font-size: 20px;
    line-height: 25px;

    letter-spacing: 0;
    margin-bottom: 1.5rem;
    text-transform: capitalize;
    font-weight: inherit;
    letter-spacing: .5px;
    color: #000000;
}
.myProperties .propertData ul li span{
	color:var(--red);
	font-weight:500;
}
/***
====================================================================
Single Properties
====================================================================
***/

.singleBlogImage {
    overflow: hidden;
}
.singleBlogImage:hover img {
    transform: scale(1.2);
}
.singleBlogImage img {
    width: 100%;
    height: auto;
    transition: all 0.3s;
}
.singleHeader {
}
.singleHeader h1,.singleHeader h3 {
    font-size: 32px;
    line-height: 36px;
    margin: 0;
    color: var(--dBlue);
    max-width: 670px;
font-weight:600;
}
.singleHeader h4 {
    font-size: 18px;
    line-height: 25px;
    margin-top: 6px;
    font-weight: 400;
    color: #888888;
}
.singleHeader h4 br {
    display: none !important;
}
.singleHeader h4 i {
    margin-right: 5px;
    margin-left: 5px;
}
.singleHeader h5 {
    position: relative;
    margin: 0 0 14px 0;
    display: inline-block;
    font-weight: 600;
    color: #333333;
    font-size: 20px;
    line-height: 22px;
}
.singleBlogContent ul li {
    list-style: none;
    padding-left: 0;
    position: relative;
}
.singleBlogContent ul li:before {
    content: "— ";
    color: #888888;
}
.singleBlogRow2 h2 {
    position: relative;
    margin: 0 0 10px 0;
    display: inline-block;
    font-size: 22px;
    line-height: 34px;
	letter-spacing:1px;
}
.sPropertyR {
    padding: 0 1em 1em;
}
.sPropertyR h4 {
    font-size: 20px;
    line-height: 1.2;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--dBlue);
}
.omBtn {
    transition: 0.3s ease-in-out !important;
    padding: 14px;
    font-size: 14px;
    line-height: 14px;
    display: inline-block;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    transition: all 0.3s;
    /* display: inline-flex; */
    gap: 5px;
    align-items: center;
    border-radius: 0;
    background: #000000;
    width: 100%;
    text-align: center;
}
.omBtn svg {
    font-size: 26px;
    fill: #ffffff;
}
.omBtn:hover {
    color: #ffffff;
    background: var(--red);
}
.singleBlogContent h3 {

    font-size: 22px;
    text-transform: capitalize;
    margin-bottom: 14px;
    letter-spacing: 1.5px;
}
h5 {
    font-size: 16px;
    line-height: 24px;
    color: #888888;
}
.propertyTable li {
    padding: 4px;
}
.agentRow .agentThumb {
    width: 80px;
    height: 80px;
    border-radius: 50px;
    background: #eeeeee;
    object-fit: cover;
}
.agentRow .agentContact h4 {
    font-size: 18px;
    color: var(--dBlue);
    margin-bottom: 6px;
	font-weight:500;
}
.agentRow .agentContact a {
    font-size: 15px;
    color: #888888;
    line-height: 18px;
	transition:all .3s;
}
.agentRow .agentContact a:hover {
    color: var(--red);
}
.propertyTable {
    display: flex;
    justify-content: flex-end;
    flex-direction: column;
    max-width: 470px;
}
.propertyTable {
}
.propertyTable li {
    display: flex;
    justify-content: space-between;
    padding: 4px;
    border-bottom: 1px solid #dddddd;
}
.propertyPrice {
    text-align: right;
}
.singleNav .nav {
    gap: 2rem;
}
.singleNav .nav.nav-tabs .nav-link {
    color: #888888;
    padding-inline: 2em;
}
.singleNav .nav.nav-tabs .nav-item.show .nav-link,
.singleNav .nav.nav-tabs .nav-link.active {
    border: none;
    border-bottom: 2px solid var(--red);
    color: var(--red);
}
.singleTabContent #myDocument {
    min-height: 70vh;
    padding-block: 2rem;
}
.singleTabContent .omBtn2 {
    padding: 1rem 1.5rem;
    color: var(--);
    font-size: 18px;
    font-weight: 500;
    background: #eeeeee;
    border-radius: 30px;
    display: inline-block;
    transition: all 0.3s;
}
.singleTabContent .omBtn2:hover {
    background: var(--dBlue);
    color: #888888;
}
.singleTabContent #map {
    height: 700px;
}
#map2 {
    height: 400px;
}
#simpleGallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(310px, 1fr));
	gap:10px;
	transition:all .3s;
	adding-block: 20px;
	
}
#simpleGallery a{
	transition:all .3s;
	
}
#simpleGallery a:hover{
	transform:scale(1.1);
	
}

#myDocument .myPdf {
    height: 700px;
}

.sl-overlay {
    background: #000000;
}
.sl-wrapper .sl-close {
    color: #ffffff;
}
.sl-wrapper .sl-navigation button {

    color: #fff;
}

@media(max-width:991px){
	.singleNav .nav.nav-tabs .nav-link {
    padding-inline: 1em;
    font-size: 15px;
}
}

@media(max-width:767px){
	.propertyPrice {
    text-align: left;
    margin-top: .5em;
}
	.singleHeader h4 {
    font-size: 16px;
    line-height: 22px;
}
	.singleTabContent #map {
    height: 500px;
}
	#map2 {
    height: 300px;
}
	.singleNav .nav {
    gap: 1rem;
}
	.singleHeader h1, .singleHeader h3 {
    font-size: 26px;
    line-height: 30px;
}
	#myDocument .myPdf {
    height: 400px;
}
	.singleTabContent #myDocument {
    min-height: initial;
}
}
@media(max-width:575px){
	
}
@media(max-width:480px){
	
}

/* New Properties Page */

.projectGrid{
	height:auto; display:grid; grid-template-columns:repeat(auto-fit,minmax(320px,1fr)); gap:30px;
}

.project-item {
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.3s ease;
    position: relative;
    color: #333333;
/*     border: 1px solid rgba(0,0,0,0.1); */
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}
.project-img {position: relative;}
.project-img .position-absolute {
    position: absolute !important;
    width: 100%;
    height: 100%;
}
.project-item  h2 {
    font-size: 18px;
    font-weight: 500;
    color: #000000;
    margin-bottom:0 !important;
	line-height:28px;
	text-transform:capitalize;
}

.project-item span.project-pill {
    position: absolute;
		top:0;
	left:0;
   background: var(--red, #000000);
    z-index: 1;
    padding: 4px 10px;
	color:#ffffff;
	font-size:14px;
	line-height:1.2;
	
}
.project-item .project-abs {
    position: absolute;
    width: 100%;
    height: auto;
    background: #000000;
    z-index: 10;
    opacity: 0;
    padding: 1rem;
	visibility:hidden;
	transition:all 0.3s;
	left: 0;
    bottom: -150%;
}

.project-item .project-abs .boldTag {
    font-size: 30px;
    color: #f7011f;
    line-height: 36px;
    font-weight: 500;
    letter-spacing: 0px;
    text-align: center;
}
.project-item .project-abs h2{
	 white-space: wrap;
	text-overflow: normal;
    color:var(--gold);
}
.project-item  h4 {
    font-size: 14px;
    color: #000000;
    font-weight: 400;
    line-height: 22px;
}
.project-item  span {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    margin: 0 !important;
}

.project-item .project-footer {
padding:16px;
}

.project-item .project-abs h4, .project-item .project-abs span{
	color:#ffffff;
}

/* .project-item:before{content:""; position:absolute; top:0; left:0; width:100%; height:100%; background:var(--gold); opacity:0; visibility:hidden; transition:.3s all ease;z-index: 99;} */
/* .project-item:after {
    content: "\f061";
    position: absolute;
    top: 20px;
    right: 20px;
	font-size:16px;
	line-height:30px;
	text-align:center;
	font-family:'Font Awesome 6 Free';
	font-weight:900;
	color:#ffffff;
    width: 30px;
    height: 30px;
    background: #000000;
    opacity: 0;
    visibility: hidden;
    transition: .3s all ease;
    z-index: 100;
	border-radius: 50%;
	transition:all 0.3s;
} */
.project-item:hover .project-abs {
    opacity: 1;
	visibility:visible;
	bottom:0;
}
.project-item .table tbody{
	 color: #ffffff;
    font-size: 14px;
}
.project-item .table>:not(caption)>*>* {
    padding: .5rem .5rem;
    color:#ffffff;
    background-color: #000;
    border-bottom-width: var(--bs-border-width);
    box-shadow: none;
}
.project-img img{width:100%}
.project-item .project-content{opacity:0; visibility:hidden;z-index:100;position:relative;}
.project-item .project-content .icon{position:absolute; top:40px; right:0; bottom:20px; width:40px; height:40px; line-height:40px; text-align:center; background:#000000; border-radius:50%; transition:all 0.3s ease; z-index:11;}
.project-item .project-content .icon i{font-size:14px; transition:all 0.3s; color:#ffffff;}
.project-item .project-content .icon:hover i{transform:translateX(3px);}
.project-item .project-content .project-inner{position:absolute; bottom:0; left:25px; transition:all 0.3s ease;}
.project-item.sold .project-content .project-inner h4{color:#ffffff;}
.project-item.sold .project-content .project-inner h3{color:#ffffff;}
.project-item.sold .project-content .project-inner h2{color:#ffffff;}
.project-item .project-content .project-inner h4{text-transform:uppercase; font-size:14px; line-height:14px; font-weight:600; margin:0;}
.project-item .project-content .project-inner h3{font-size:35px; line-height:35px; font-weight:700; margin:0; text-transform:uppercase;}
.project-item .project-content .project-inner h2{font-size:25px; line-height:35px; font-weight:700; margin:0;}
.project-item:hover:before{opacity:0.8; visibility:visible;}
.project-item:hover .project-content{opacity:1; visibility:visible;}
.project-item:hover .project-content .icon{right:25px;}
.project-item:hover .project-content .project-inner{bottom:20px;}


