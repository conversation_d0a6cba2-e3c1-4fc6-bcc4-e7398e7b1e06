(function ($) {
  "use strict";

  /**
   * All of the code for your public-facing JavaScript source
   * should reside in this file.
   *
   * Note: It has been assumed you will write jQuery code here, so the
   * $ function reference has been prepared for usage within the scope
   * of this function.
   *
   * This enables you to define handlers, for when the DOM is ready:
   */
  $(function () {

    var SwiftFilter = {
        propertyTypeCheckboxes: function () {
            var propertyTypesPlaceholder = jQuery('.property-placeholder');
            jQuery(".filter-properties-all").each(function (index) {
                var group = jQuery(this).data("group");
                var parent = jQuery(this);
                parent.change(function () { //"select all" change
                    jQuery(group).prop('checked', parent.prop("checked"));
    
                    SwiftFilter.loadProperties();
    
                    propertyTypesPlaceholder.html('All');
                });
                if (jQuery(group + ':checked').length == jQuery(group).length) {
                    parent.prop('checked', true);
                    propertyTypesPlaceholder.html('All');
                }
                jQuery(group).change(function () {
                    parent.prop('checked', false);
    
                    SwiftFilter.loadProperties();
    
                    if (jQuery(group + ':checked').length == jQuery(group).length) {
                        parent.prop('checked', true);
                        propertyTypesPlaceholder.html('All');
                    } else {
                        propertyTypesPlaceholder.html('All');
                    }
                    var propertySelected = [];
                    jQuery(group + ':checked').each(function () {
                        propertySelected.push(jQuery(this).data('name'));
                        propertyTypesPlaceholder.html(propertySelected.join(','));
                    });
                });
            });
        },

        transactionTypeCheckboxes: function () {
            var transactionTypesPlaceholder = jQuery('.transaction-placeholder');
            jQuery(".filter-transaction-all").each(function (index) {
                var group = jQuery(this).data("group");
                var parent = jQuery(this);
                parent.change(function () { //"select all" change
                    jQuery(group).prop('checked', parent.prop("checked"));
    
                    SwiftFilter.loadProperties();
    
                    transactionTypesPlaceholder.html('All');
                });
                if (jQuery(group + ':checked').length == jQuery(group).length) {
                    parent.prop('checked', true);
                    transactionTypesPlaceholder.html('All');
                }
                jQuery(group).change(function () {
                    parent.prop('checked', false);
    
                    SwiftFilter.loadProperties();
    
                    if (jQuery(group + ':checked').length == jQuery(group).length) {
                        parent.prop('checked', true);
                        transactionTypesPlaceholder.html('All');
                    } else {
                        transactionTypesPlaceholder.html('All');
                    }
                    var transactionSelected = [];
                    jQuery(group + ':checked').each(function () {
                        transactionSelected.push(jQuery(this).data('name'));
                        transactionTypesPlaceholder.html(transactionSelected.join(','));
                    });
                });
            });
        },
    
        filterClicks: function (){
            var priceFrom = jQuery("#price-from");
            var priceTo = jQuery("#price-to");
            var pricePlaceholder = jQuery(".price-placeholder");
        
            priceFrom.on('keyup', function (e) {
                pricePlaceholder.html('$' + jQuery(this).val() + ' - $' + priceTo.val());
                if (e.keyCode === 13) {
                   SwiftFilter.loadProperties();
                }
            });
            priceTo.on('keyup', function (e) {
                pricePlaceholder.html('$' + priceFrom.val() + ' - $' + jQuery(this).val());
                if (e.keyCode === 13) {
                    SwiftFilter.loadProperties();
                }
            });

            priceFrom.on('blur', function (e) {
                pricePlaceholder.html('$' + jQuery(this).val() + ' - $' + priceTo.val());
                SwiftFilter.loadProperties();

            });
            priceTo.on('blur', function (e) {
                pricePlaceholder.html('$' + priceFrom.val() + ' - $' + jQuery(this).val());
                SwiftFilter.loadProperties();
            });
        },
        dropDownInit: function () {
            jQuery('.filter-onclick').on('click', function (e) {
                var dropdown = jQuery(this).next('.filter-dropdown');
                //jQuery('.filter-dropdown').not(dropdown).addClass('bts-hide');
                jQuery('.filter-dropdown').not(dropdown).css('display', 'none'); // hide all other dropdown 
                //dropdown.toggleClass('bts-hide');
                dropdown.slideToggle('fast', 'linear');
            });
            jQuery(document).click(function (event) {
                //if you click on anything except the modal itself or the "open modal" link, close the modal
                if (!jQuery(event.target).closest(".filter-select, .filter-dropdown").length) {
                    //jQuery("body").find(".filter-dropdown").addClass("bts-hide");
                    jQuery("body").find(".filter-dropdown").css('display', 'none');
                }
            }); 
        },
        mapInfoHtml: function(locationURL, locationType, locationImg, locationTitle, city_state) {
            return ('<div class="map-popup-wrap">' +
                '<div class="map-popup"><div class="infoBox-close"><i class="fa fa-times"></i></div>' +
                '<div class="map-popup-category">' + locationType + '</div>' +
                '<a href="' + locationURL + '" class="listing-img-content fl-wrap">' +
                '<img src="' + locationImg + '" alt=""></a> <div class="listing-content fl-wrap">' +
            
                '<div class="listing-title fl-wrap"><h4><a href=' + locationURL + '>' + locationTitle + '</a></h4></div>' +
                '</div></div></div>')
        },
       
        loadProperties: function () {
            
            var transaction_types = [];
            var properties_types = [];

            jQuery('input[name="transaction_type"]:checked').each(function () {
                transaction_types.push(this.value);
            });
            jQuery('input[name="property_type"]:checked').each(function () {
                properties_types.push(this.value);
            });

            var data = {
                'action': 'SwiftFilter',
                'transaction_type': transaction_types.join(),
                'property_type': properties_types.join(),
                'min_price': jQuery('#price-from').val(),
                'max_price': jQuery('#price-to').val()
            }

            $.ajax({
              url: SWIFT_FILTER_VARS.ajax_url,
              type: "POST",
              data: data,
              beforeSend: function () {
                // Display a loading spinner or message
              },
              success: function (response) {
                $("#property-list-container").html(response.html);
             
              },
              complete: function () {
                // Hide the loading spinner or message
              },
            });
          },
        init: function () {
            SwiftFilter.loadProperties();
            SwiftFilter.propertyTypeCheckboxes();
            SwiftFilter.transactionTypeCheckboxes();
            SwiftFilter.dropDownInit();
            SwiftFilter.filterClicks();
            // var map = document.getElementById('map-main');
            // if (typeof (map) != 'undefined' && map != null) {
            //     google.maps.event.addDomListener(window, 'load', SwiftFilter.initMap);
            // }
        }
    };
    
    $(document).ready(function () {
        SwiftFilter.init();

    });

  });


})(jQuery);
