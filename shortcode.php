<?php

// Shortcode function
function swift_cre_filter_shortcode_function($atts)
{
    ob_start(); // Start output buffering
?>
    <div class="container-fluid projectsTop pt-4 pb-2">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-10 col-xl-8">
                <div class="row">
                    <div class="col-md-3">
                        <div class="filter-transaction-types-wrapper ">
                            <label class="filter-filter-label-md">Transaction Types</label>
                            <div class="filter-select">
                                <span class="filter-placeholder transaction-placeholder">All</span>
                                <span class="filter-select-arrow "></span>
                                <span class="filter-onclick"></span>
                                <div class="filter-dropdown" style="display: none;">
                                    <div class="col-md-12">
                                        <input id="parent3-label" type="checkbox" class="filter-transaction-all" data-group=".filter-transaction">
                                        <label for="parent3-label" class="text-black">All</label><br>
                                        <ul class="transaction-type-dropdown">

                                        <?php
                                        $transaction_types =  get_terms([
                                            "taxonomy" => "transaction",
                                            "hide_empty" => true,
                                        ]);
                                        $t_counter = 0;
                                        foreach ($transaction_types as $transaction ): ?>
                                         <li>
                                            <input type="checkbox" class="filter-transaction" data-category="<?=$transaction->slug?>" data-name="<?=$transaction->name?>" name="transaction_type" id="t-<?=$t_counter?>" value="<?=$transaction->slug?>">
                                            <label for="t-<?=$t_counter?>" class="text-black"><?php echo esc_html($transaction->name); ?></label>
                                        </li>
                                        <?php 
                                        $t_counter++;
                                        endforeach; ?>

                                           

                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="filter-property-types-wrapper ">
                            <label class="filter-filter-label-md">Property Types</label>
                            <div class="filter-select">
                                <span class="filter-placeholder property-placeholder">All</span>
                                <span class="filter-select-arrow "></span>
                                <span class="filter-onclick"></span>
                                <div class="filter-dropdown" style="display: none;">
                                    <div class="col-md-12">
                                        <input id="parent3-label" type="checkbox" class="filter-properties-all" data-group=".filter-properties">
                                        <label for="parent3-label" class="text-black">All</label><br>
                                        <ul class="property-type-dropdown">

                                        <?php
                                        $property_types =  get_terms([
                                            "taxonomy" => "property_type",
                                            "hide_empty" => true,
                                        ]);
                                        $p_counter = 0;
                                        foreach ($property_types as $property_type ): ?>
                                         <li>
                                            <input type="checkbox" class="filter-properties" data-category="<?=$property_type->slug?>" data-name="<?=$property_type->name?>" name="property_type" id="p-<?=$p_counter?>" value="<?=$property_type->slug?>">
                                            <label for="p-<?=$p_counter?>" class="text-black"><?php echo esc_html($property_type->name); ?></label>
                                        </li>
                                        <?php 
                                        $p_counter++;
                                        endforeach; ?>

                                           

                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">

                        <div class="filter-price-wrapper price-rang">

                            <label class="filter-label-md">Price</label>
                            <div class="filter-select">
                                <span class="filter-placeholder price-placeholder">Select</span>
                                <span class="filter-select-arrow "></span>
                                <span class="filter-onclick"></span>
                                <div class="filter-dropdown" style="display: none;">
                                    <div class="filter-price-range">
                                        <div class="filter-extra-controls ">
                                            <div>
                                                <input type="text" value="" id="price-from" placeholder="Min: 0" class="inp" />
                                            </div>
                                            <div>
                                                <input type="text" value="" id="price-to" placeholder="Max: 25000000" class="inp" />
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>



                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid myProperties pb-5">
        <div class="container">
           
           
                <div class="projectGrid" id="property-list-container"></div>
       
        </div>
    </div>
<?php return ob_get_clean(); // Return the buffered content
}

add_shortcode('swift_cre_filter', 'swift_cre_filter_shortcode_function');
