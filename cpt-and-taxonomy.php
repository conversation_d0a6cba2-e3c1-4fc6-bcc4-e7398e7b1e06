<?php


// CPT and Taxonomies
function swift_filter_register_cpt()
{
    $properties_labels = [
        "name" => _x("Properties", "post type general name"),
        "singular_name" => _x("Properties", "post type singular name"),
        "add_new" => _x("Add New", "book"),
        "add_new_item" => __("Add New Property"),
        "edit_item" => __("Edit Property"),
        "new_item" => __("New Property"),
        "all_items" => __("All Properties"),
        "view_item" => __("View Property"),
        "search_items" => __("Search Property"),
        "not_found" => __("No Property found"),
        "not_found_in_trash" => __("No Property found in the Trash"),
        "parent_item_colon" => "",
        "menu_name" => "Properties",
    ];
    $properties_args = [
        "labels" => $properties_labels,
        "description" => "",
        "public" => true,
        "menu_position" => 18,
        "supports" => ["title", "editor", "thumbnail"],
        "has_archive" => true,
    ];
    register_post_type("our_properties", $properties_args);
 
 
    $transaction_labels = [
        "name" => _x("Transaction", "taxonomy general name", "textdomain"),
        "singular_name" => _x(
            "Transaction",
            "taxonomy singular name",
            "textdomain"
        ),
        "search_items" => __("Search Transactions", "textdomain"),
        "popular_items" => __("Popular Transactions", "textdomain"),
        "all_items" => __("All Transaction", "textdomain"),
        "parent_item" => __("Parent Transaction", "textdomain"),
        "parent_item_colon" => __("Parent Transaction:", "textdomain"),
        "edit_item" => __("Edit Transaction", "textdomain"),
        "update_item" => __("Update Transaction", "textdomain"),
        "add_new_item" => __("Add New Transaction", "textdomain"),
        "new_item_name" => __("New Transaction Name", "textdomain"),
        "separate_items_with_commas" => __(
            "Separate transactions with commas",
            "textdomain"
        ),
        "add_or_remove_items" => __("Add or remove transaction", "textdomain"),
        "choose_from_most_used" => __(
            "Choose from the most used transactions",
            "textdomain"
        ),
        "menu_name" => __("Transactions", "textdomain"),
    ];

    $transaction_args = [
        "hierarchical" => true,
        "labels" => $transaction_labels,
        "public" => true,
        "show_ui" => true,
        "show_admin_column" => true,
        "show_in_nav_menus" => true,
        "show_tagcloud" => true,
        "rewrite" => ["slug" => "transaction"],
    ];

    register_taxonomy("transaction", "our_properties", $transaction_args);
 
    $property_type_labels = [
        "name" => _x("Property Type", "taxonomy general name", "textdomain"),
        "singular_name" => _x(
            "Property Type",
            "taxonomy singular name",
            "textdomain"
        ),
        "search_items" => __("Search Transactions", "textdomain"),
        "popular_items" => __("Popular Transactions", "textdomain"),
        "all_items" => __("All Property Types", "textdomain"),
        "parent_item" => __("Parent Transaction", "textdomain"),
        "parent_item_colon" => __("Parent Property Type:", "textdomain"),
        "edit_item" => __("Edit Property Type", "textdomain"),
        "update_item" => __("Update Property Type", "textdomain"),
        "add_new_item" => __("Add New Property Type", "textdomain"),
        "new_item_name" => __("New Property Type Name", "textdomain"),
        "separate_items_with_commas" => __(
            "Separate property types with commas",
            "textdomain"
        ),
        "add_or_remove_items" => __(
            "Add or remove property type",
            "textdomain"
        ),
        "choose_from_most_used" => __(
            "Choose from the most used transactions",
            "textdomain"
        ),
        "menu_name" => __("Property Types", "textdomain"),
    ];

    $property_type_args = [
        "hierarchical" => true,
        "labels" => $property_type_labels,
        "public" => true,
        "show_ui" => true,
        "show_admin_column" => true,
        "show_in_nav_menus" => true,
        "show_tagcloud" => true,
        "rewrite" => ["slug" => "property_type"],
    ];

    register_taxonomy("property_type", "our_properties", $property_type_args);


}


add_action("init", "swift_filter_register_cpt", 1);
